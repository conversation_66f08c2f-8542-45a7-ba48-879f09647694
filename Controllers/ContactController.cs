using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using v2.backend.Models;
using v2.backend.Models.CRMModels;
using v2.backend.Services;
using static v2.backend.Models.PropertyModels;

namespace v2.backend.Controllers
{
    public class ContactController : Controller
    {
        private readonly AppDbContext _context;
        private readonly AuthService _authService;
        private readonly IContactService _contactService;
        private readonly IAppDbContextProvider _dbContextProvider;
        private readonly HttpClient _httpClient;
        private readonly TrackService _trackService;
        private readonly string _apiToken;
        private readonly RedisService _redisService;

        public ContactController(AppDbContext context, TrackService trackService, AuthService authService, IAppDbContextProvider dbContextProvider, IContactService contactService, RedisService redisService)
        {
            _context = context;
            _authService = authService;
            _trackService = trackService;
            _contactService = contactService;
            _dbContextProvider = dbContextProvider;
            _redisService = redisService;

            // _httpClient = httpClientFactory.CreateClient();
            // _apiToken = config["HubSpot:ApiToken"];
            // _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _apiToken);
        }
        [HttpGet("F4012DB6")]
        public async Task<ActionResult<IEnumerable<Contact>>> GetAllContactsAndStoreInCache(
                    [FromQuery] int page = 1,
                    [FromQuery] int pageSize = 30,
                    [FromQuery] string filter = "alphabetical")
        {
            try
            {
                var userId = await _authService.GetUser(Request);
                if (userId == Guid.Empty)
                {
                    return Unauthorized();
                }
                var userData = await _context.Users.FirstOrDefaultAsync(w => w.Id == userId && w.IsActive);
                var userEmail = await _context.UserEmails.FirstOrDefaultAsync(w => w.UserId == userId && w.IsActive);

                List<Contact> allContacts = new List<Contact>();
                try
                {
                    var customerData = await _context.Customers.FirstOrDefaultAsync(w => w.OrgId == userData.OrgId && w.IsActive);
                    using var dbContext = await _dbContextProvider.GetDbContextAsync(customerData.Id);


                    var ownerMappingData = await dbContext.userownermapping.FirstOrDefaultAsync(w => w.useremailid == userEmail.Email);
                    if (ownerMappingData != null)
                    {
                        var ownerId = ownerMappingData.ownerid;

                        var crmContacts = await dbContext.ccontact
                            .Where(w => w.OwnerId == ownerId && w.IsActive == true)
                            .ToListAsync();

                        foreach (var contactData in crmContacts)
                        {
                            Contact contact = new Contact()
                            {
                                Id = contactData.Id,
                                UserId = userId,
                                OrgId = userData.OrgId,
                                CrmId = contactData.CrmId,
                                FirstName = contactData.FirstName,
                                LastName = contactData.LastName,
                                DisplayName = contactData.DisplayName,
                                Email = contactData.Email,
                                Designation = contactData.Designation,
                                Linkedin = !string.IsNullOrEmpty(contactData.Linkedin) ? contactData.Linkedin : contactData.hs_linkedin_url,
                                IsActive = contactData.IsActive,
                                CreatedDate = contactData.CreatedDate,
                                ModifiedDate = contactData.ModifiedDate,
                                ProviderId = contactData.ProviderId,
                                IsEnriched = contactData.IsEnriched
                            };
                            allContacts.Add(contact);
                        }
                    }
                    else
                    {
                        var crmContacts = await dbContext.ccontact
                            .Where(w => w.UserId == userId && w.IsActive == true)
                            .ToListAsync();

                        foreach (var contactData in crmContacts)
                        {
                            Contact contact = new Contact()
                            {
                                Id = contactData.Id,
                                UserId = userId,
                                OrgId = userData.OrgId,
                                CrmId = contactData.CrmId,
                                FirstName = contactData.FirstName,
                                LastName = contactData.LastName,
                                DisplayName = contactData.DisplayName,
                                Email = contactData.Email,
                                Designation = contactData.Designation,
                                Linkedin = !string.IsNullOrEmpty(contactData.Linkedin) ? contactData.Linkedin : contactData.hs_linkedin_url,
                                IsActive = contactData.IsActive,
                                CreatedDate = contactData.CreatedDate,
                                ModifiedDate = contactData.ModifiedDate,
                                ProviderId = contactData.ProviderId,
                                IsEnriched = contactData.IsEnriched
                            };
                            allContacts.Add(contact);
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Handle exception (log it, etc.)
                }

                var contacts = await _contactService.GetAllContactAsync(userId);
                allContacts.AddRange(contacts);

                string cacheKey = $"contacts_{userId}";

                // Cache the result
                var serialized = System.Text.Json.JsonSerializer.Serialize(allContacts);
                await _redisService.SetStringAsync(cacheKey, serialized, TimeSpan.FromHours(72));
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.ToString());
            }
        }
        [HttpGet("D19EDEBA")]
        public async Task<ActionResult<IEnumerable<Contact>>> GetContacts(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 30,
            [FromQuery] string filter = "alphabetical")
        {
            var userId = await _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            var userData = await _context.Users.FirstOrDefaultAsync(w => w.Id == userId && w.IsActive);
            var userEmail = await _context.UserEmails.FirstOrDefaultAsync(w => w.UserId == userId && w.IsActive);

            List<Contact> allContacts = new List<Contact>();
            string cacheKey = $"contacts_{userId}";
            // Try to get from cache
            // var cached = await _redisService.GetStringAsync(cacheKey);
            // if (!string.IsNullOrEmpty(cached))
            // {
            //     var cachedModel = System.Text.Json.JsonSerializer.Deserialize<List<Contact>>(cached);
            //     return Ok(cachedModel);
            // }
            try
            {
                var customerData = await _context.Customers.FirstOrDefaultAsync(w => w.OrgId == userData.OrgId && w.IsActive);
                using var dbContext = await _dbContextProvider.GetDbContextAsync(customerData.Id);


                var ownerMappingData = await dbContext.userownermapping.FirstOrDefaultAsync(w => w.useremailid == userEmail.Email);
                if (ownerMappingData != null)
                {
                    var ownerId = ownerMappingData.ownerid;

                    var crmContactsQuery = dbContext.ccontact
                        .Where(w => w.OwnerId == ownerId && w.IsActive == true);

                    // Apply sorting based on the filter
                    if (filter.ToLower() == "latest")
                    {
                        crmContactsQuery = crmContactsQuery.OrderByDescending(w => w.ModifiedDate);
                    }
                    else
                    {
                        crmContactsQuery = crmContactsQuery.OrderBy(w => w.DisplayName);
                    }

                    var crmContacts = await crmContactsQuery
                        .Skip((page - 1) * pageSize)
                        .Take(pageSize)
                        .ToListAsync();

                    foreach (var contactData in crmContacts)
                    {
                        Contact contact = new Contact()
                        {
                            Id = contactData.Id,
                            UserId = userId,
                            OrgId = userData.OrgId,
                            CrmId = contactData.CrmId,
                            FirstName = contactData.FirstName,
                            LastName = contactData.LastName,
                            DisplayName = contactData.DisplayName,
                            Email = contactData.Email,
                            Designation = contactData.Designation,
                            Linkedin = !string.IsNullOrEmpty(contactData.Linkedin) ? contactData.Linkedin : contactData.hs_linkedin_url,
                            IsActive = contactData.IsActive,
                            CreatedDate = contactData.CreatedDate,
                            ModifiedDate = contactData.ModifiedDate,
                            ProviderId = contactData.ProviderId,
                            IsEnriched = contactData.IsEnriched
                        };
                        allContacts.Add(contact);
                    }
                }
                else
                {
                    var crmContactsQuery = dbContext.ccontact
                        .Where(w => w.UserId == userId && w.IsActive == true);

                    // Apply sorting based on the filter
                    if (filter.ToLower() == "latest")
                    {
                        crmContactsQuery = crmContactsQuery.OrderByDescending(w => w.ModifiedDate);
                    }
                    else
                    {
                        crmContactsQuery = crmContactsQuery.OrderBy(w => w.DisplayName);
                    }

                    var crmContacts = await crmContactsQuery
                        .Skip((page - 1) * pageSize)
                        .Take(pageSize)
                        .ToListAsync();

                    foreach (var contactData in crmContacts)
                    {
                        Contact contact = new Contact()
                        {
                            Id = contactData.Id,
                            UserId = userId,
                            OrgId = userData.OrgId,
                            CrmId = contactData.CrmId,
                            FirstName = contactData.FirstName,
                            LastName = contactData.LastName,
                            DisplayName = contactData.DisplayName,
                            Email = contactData.Email,
                            Designation = contactData.Designation,
                            Linkedin = !string.IsNullOrEmpty(contactData.Linkedin) ? contactData.Linkedin : contactData.hs_linkedin_url,
                            IsActive = contactData.IsActive,
                            CreatedDate = contactData.CreatedDate,
                            ModifiedDate = contactData.ModifiedDate,
                            ProviderId = contactData.ProviderId,
                            IsEnriched = contactData.IsEnriched
                        };
                        allContacts.Add(contact);
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exception (log it, etc.)
            }

            var contacts = await _contactService.GetAllContactAsync(userId);
            allContacts.AddRange(contacts);

            // Apply sorting to the combined list
            if (filter.ToLower() == "latest")
            {
                allContacts = allContacts.OrderByDescending(c => c.ModifiedDate).ToList();
            }
            else
            {
                allContacts = allContacts.OrderBy(c => c.DisplayName).ToList();
            }

            // Apply pagination to the sorted list
            var skipCount = (page - 1) * pageSize;
            if (skipCount >= allContacts.Count)
            {
                return Ok(new List<Contact>()); // Return an empty list if skipCount exceeds total count
            }

            var paginatedContacts = allContacts
                .Skip(skipCount)
                .Take(pageSize)
                .ToList();

            return Ok(paginatedContacts);
        }

        [HttpGet("B49DF23F/{id}")]
        public async Task<ActionResult<Contact>> GetContact(Guid id)
        {
            var userId = await _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            var contact = await _contactService.GetContactByIdAsync(id);

            if (contact == null)
            {
                try
                {
                    var userData = await _context.Users.FirstOrDefaultAsync(w => w.Id == userId && w.IsActive);
                    var customerData = await _context.Customers.FirstOrDefaultAsync(w => w.OrgId == userData.OrgId && w.IsActive);
                    using var dbContext = await _dbContextProvider.GetDbContextAsync(customerData.Id);

                    var contactData = await dbContext.ccontact.FirstOrDefaultAsync(w => w.Id == id && w.IsActive == true);
                    if (contactData != null)
                    {
                        Contact contact1 = new Contact()
                        {
                            Id = contactData.Id,
                            UserId = userId,
                            OrgId = userData.OrgId,
                            CrmId = contactData.CrmId,
                            FirstName = contactData.FirstName,
                            LastName = contactData.LastName,
                            DisplayName = contactData.DisplayName,
                            Email = contactData.Email,
                            Designation = contactData.Designation,
                            Linkedin = !string.IsNullOrEmpty(contactData.Linkedin) ? contactData.Linkedin : contactData.hs_linkedin_url,
                            IsActive = contactData.IsActive,
                            CreatedDate = contactData.CreatedDate,
                            ModifiedDate = contactData.ModifiedDate,
                            ProviderId = contactData.ProviderId,
                            IsEnriched = contactData.IsEnriched
                        };
                        return Ok(contact1);
                    }
                    return NotFound();
                }
                catch (Exception ex)
                {
                    return BadRequest(ex.ToString());
                }
            }
            return Ok(contact);
        }
        public class ContactPayload
        {
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public string Email { get; set; }
            public string Linkedin { get; set; }
            public string Designation { get; set; }
        }

        [HttpPost("29E47320")]
        public async Task<ActionResult<Company>> CreateContact([FromBody] ContactPayload contactPayload)
        {
            var userId = await _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            var userData = await _context.Users.FirstOrDefaultAsync(w => w.Id == userId && w.IsActive);
            var customerData = await _context.Customers.FirstOrDefaultAsync(w => w.OrgId == userData.OrgId && w.IsActive);
            using var dbContext = await _dbContextProvider.GetDbContextAsync(customerData.Id);

            if (!string.IsNullOrEmpty(contactPayload.Email))
            {
                var domain = contactPayload.Email.Split('@')[1];
                // Normalize domain for comparison (remove www. if present)
                string NormalizeDomain(string d)
                {
                    if (string.IsNullOrEmpty(d)) return "";
                    d = d.ToLower();
                    if (d.StartsWith("www.")) d = d.Substring(4);
                    return d;
                }
                var normalizedInputDomain = NormalizeDomain(domain);
                var possibleCompanies = await dbContext.ccompany
                    .Where(w => w.UserId == userId && w.IsActive == true)
                    .ToListAsync();
                var checkForCompany = possibleCompanies.FirstOrDefault(w =>
                    NormalizeDomain(w.Domain) == normalizedInputDomain || NormalizeDomain(w.Website) == normalizedInputDomain);
                if (checkForCompany == null)
                {
                    CCompany company = new CCompany()
                    {
                        Id = Guid.NewGuid(),
                        Name = domain.Split('.')[0], // Use the first part of the domain as the company name
                        Domain = domain,
                        Website = $"https://{domain}",
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        IsActive = true,
                        UserId = userId,
                        OrgId = _context.Users.FirstOrDefault(w => w.Id == userId).OrgId,
                        CustomerId = customerData.Id,
                        ProviderId = Guid.Empty,
                        IsSynced = false,
                        IsEnriched = false
                    };
                    dbContext.ccompany.Add(company);
                    await dbContext.SaveChangesAsync();

                    // store in the redis cache
                    string cacheKey = $"companies_{userId}";
                    var cached = await _redisService.GetStringAsync(cacheKey);
                    if (!string.IsNullOrEmpty(cached))
                    {
                        var cachedModel = System.Text.Json.JsonSerializer.Deserialize<List<Company>>(cached);
                        Company companyData = new Company()
                        {
                            Id = company.Id,
                            UserId = userId,
                            CrmId = company.CrmId,
                            Name = (!string.IsNullOrEmpty(company.Name)) ? company.Name : company.Domain,
                            Domain = company.Domain,
                            Website = company.Website,
                            Linkedin = company.Linkedin,
                            Facebook = company.Facebook,
                            X = company.X,
                            Hq = company.Hq,
                            Addresses = company.Addresses,
                            Locations = company.Locations,
                            IsActive = company.IsActive,
                            CreatedDate = company.CreatedDate,
                            ModifiedDate = company.ModifiedDate,
                            ProviderId = company.ProviderId,
                            OtherDomains = company.OtherDomains,
                            IsEnriched = company.IsEnriched
                        };
                        cachedModel.AddRange(companyData);
                        var serialized = System.Text.Json.JsonSerializer.Serialize(cachedModel);
                        await _redisService.SetStringAsync(cacheKey, serialized, TimeSpan.FromHours(72));

                    }

                    try
                    {
                        // track company creation
                        await _trackService.CreateTrack(userId, new TrackPayload()
                        {
                            Page = "Company",
                            Element = "CompanyAdded",
                            PageAction = "Create"
                        });
                    }
                    catch (Exception ex)
                    {

                    }
                }
            }
            bool isContactExists = false;
            if (!string.IsNullOrEmpty(contactPayload.Email))
            {
                var checkForContact = await dbContext.ccontact.FirstOrDefaultAsync(w => w.Email == contactPayload.Email && w.UserId == userId && w.IsActive == true);
                if (checkForContact != null)
                {
                    isContactExists = true;
                }
            }
            else if (!string.IsNullOrEmpty(contactPayload.Linkedin))
            {
                var checkForContact = await dbContext.ccontact.FirstOrDefaultAsync(w => w.Linkedin == contactPayload.Linkedin && w.UserId == userId && w.IsActive == true);
                if (checkForContact != null)
                {
                    isContactExists = true;
                }
            }
            if (!isContactExists)
            {
                CContact contact = new CContact();
                contact.Id = Guid.NewGuid();
                contact.CreatedDate = DateTime.UtcNow;
                contact.Designation = contactPayload.Designation;
                contact.Email = contactPayload.Email;
                contact.FirstName = contactPayload.FirstName;
                contact.LastName = contactPayload.LastName;
                contact.DisplayName = contactPayload.FirstName + " " + contactPayload.LastName;
                contact.IsActive = true;
                contact.Linkedin = contactPayload.Linkedin;
                contact.UserId = userId;
                contact.ModifiedDate = DateTime.UtcNow;
                contact.OrgId = _context.Users.FirstOrDefault(w => w.Id == userId).OrgId;
                contact.IsEnriched = false;

                dbContext.ccontact.Add(contact);
                await dbContext.SaveChangesAsync();


                // store in the redis cache
                string cacheKey = $"contacts_{userId}";
                var cached = await _redisService.GetStringAsync(cacheKey);
                if (!string.IsNullOrEmpty(cached))
                {
                    var cachedModel = System.Text.Json.JsonSerializer.Deserialize<List<Contact>>(cached);
                    Contact contactData = new Contact()
                    {
                        Id = contact.Id,
                        UserId = userId,
                        OrgId = userData.OrgId,
                        CrmId = contact.CrmId,
                        FirstName = contact.FirstName,
                        LastName = contact.LastName,
                        DisplayName = contact.DisplayName,
                        Email = contact.Email,
                        Designation = contact.Designation,
                        Linkedin = !string.IsNullOrEmpty(contact.Linkedin) ? contact.Linkedin : contact.hs_linkedin_url,
                        IsActive = contact.IsActive,
                        CreatedDate = contact.CreatedDate,
                        ModifiedDate = contact.ModifiedDate,
                        ProviderId = contact.ProviderId,
                        IsEnriched = contact.IsEnriched
                    };
                    cachedModel.AddRange(contactData);
                    var serialized = System.Text.Json.JsonSerializer.Serialize(cachedModel);
                    await _redisService.SetStringAsync(cacheKey, serialized, TimeSpan.FromHours(72));
                }

                try
                {
                    // track contact creation
                    await _trackService.CreateTrack(userId, new TrackPayload()
                    {
                        Page = "Contact",
                        Element = "ContactAdded",
                        PageAction = "Create"
                    });
                }
                catch (Exception ex)
                {

                }

                try
                {
                    // create user in neo4j
                    using (var httpClient1 = new HttpClient())
                    {
                        var payload = new CreateNeo4jContactModel()
                        {
                            displayName = contact.DisplayName,
                            email = contact.Email,
                            firstName = contact.FirstName,
                            id = contact.Id.ToString(),
                            lastName = contact.LastName,
                            linkedin = contact.Linkedin,
                            providerId = contact.ProviderId.ToString(),
                            designation = contact.Designation,
                            crmId = contact.CrmId,
                        };
                        var request1 = new HttpRequestMessage(HttpMethod.Post, $"https://saleslife-neo4j-api-811039469089.europe-north1.run.app/api/v2/{userData.OrgId}/users/{userId}/B381F330")
                        {
                            // Optionally set content if the POST requires a body
                            Content = new StringContent(JsonConvert.SerializeObject(payload), System.Text.Encoding.UTF8, "application/json")
                        };
                        var TokenData = await _context.Tokens.FirstOrDefaultAsync(w => w.UserId == userId && w.IsActive);
                        request1.Headers.Add("authToken", TokenData?.Id.ToString());
                        // Fire-and-forget the Neo4j API call
                        Task.Run(async () =>
                        {
                            try
                            {
                                var response1 = await httpClient1.SendAsync(request1);
                                if (!response1.IsSuccessStatusCode)
                                {
                                    // Optionally log the failure or handle it as needed
                                    Console.WriteLine($"Neo4j API call failed with status code: {response1.StatusCode}");
                                }
                                else
                                {
                                    // Optionally log the success
                                    Console.WriteLine("Neo4j API call succeeded.");
                                }
                            }
                            catch (Exception ex)
                            {
                                // Optionally log the exception
                                Console.WriteLine($"Neo4j API call failed: {ex.Message}");
                            }
                        });
                    }
                }
                catch (Exception ex)
                {

                }

            }

            return Ok();
        }

        [HttpPut("1DD34893/{id}")]
        public async Task<IActionResult> UpdateContact(Guid id, [FromBody] ContactPayload contact)
        {
            var userId = await _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }


            var contactData = await _context.Contacts.FirstOrDefaultAsync(w => w.Id == id);
            if (contactData == null)
            {
                var userData = await _context.Users.FirstOrDefaultAsync(w => w.Id == userId && w.IsActive);
                var customerData = await _context.Customers.FirstOrDefaultAsync(w => w.OrgId == userData.OrgId && w.IsActive);
                using var dbContext = await _dbContextProvider.GetDbContextAsync(customerData.Id);
                var checkForCContact = await dbContext.ccontact.FirstOrDefaultAsync(w => w.Id == id);
                if (checkForCContact != null)
                {
                    checkForCContact.Designation = contact.Designation;
                    checkForCContact.FirstName = contact.FirstName;
                    checkForCContact.LastName = contact.LastName;
                    checkForCContact.Email = contact.Email;
                    checkForCContact.Linkedin = contact.Linkedin;
                    await dbContext.SaveChangesAsync();

                    // Invalidate cache after successful database update
                    // This ensures cache consistency and avoids race conditions
                    string cacheKey = $"contacts_{userId}";
                    try
                    {
                        await _redisService.RemoveAsync(cacheKey);
                        // Cache will be repopulated on next read request
                    }
                    catch (Exception cacheEx)
                    {
                        // Log cache invalidation failure but don't fail the request
                        // Database update was successful, cache will eventually be consistent
                        // TODO: Add proper logging here
                        Console.WriteLine($"Failed to invalidate cache for key {cacheKey}: {cacheEx.Message}");
                    }

                    try
                    {
                        // track contact update
                        await _trackService.CreateTrack(userId, new TrackPayload()
                        {
                            Page = "Contact",
                            Element = "ContactUpdated",
                            PageAction = "Update"
                        });
                    }
                    catch (Exception ex)
                    {

                    }
                }
            }
            else
            {
                contactData.Designation = contact.Designation;
                contactData.FirstName = contact.FirstName;
                contactData.LastName = contact.LastName;
                contactData.Email = contact.Email;
                contactData.Linkedin = contact.Linkedin;

                var updatedContact = await _contactService.UpdateContactAsync(contactData);

                // Invalidate cache after successful database update
                // This ensures cache consistency and avoids race conditions
                string cacheKey = $"contacts_{userId}";
                try
                {
                    await _redisService.RemoveAsync(cacheKey);
                    // Cache will be repopulated on next read request
                }
                catch (Exception cacheEx)
                {
                    // Log cache invalidation failure but don't fail the request
                    // Database update was successful, cache will eventually be consistent
                    // TODO: Add proper logging here
                    Console.WriteLine($"Failed to invalidate cache for key {cacheKey}: {cacheEx.Message}");
                }

                try
                {
                    // track contact update
                    await _trackService.CreateTrack(userId, new TrackPayload()
                    {
                        Page = "Contact",
                        Element = "ContactUpdated",
                        PageAction = "Update"
                    });
                }
                catch (Exception ex)
                {

                }
            }

            try
            {
                // create user in neo4j
                using (var httpClient1 = new HttpClient())
                {
                    var payload = new CreateNeo4jContactModel()
                    {
                        displayName = contactData.DisplayName,
                        email = contactData.Email,
                        firstName = contactData.FirstName,
                        id = contactData.Id.ToString(),
                        lastName = contactData.LastName,
                        linkedin = contactData.Linkedin,
                        providerId = contactData.ProviderId.ToString(),
                        designation = contactData.Designation,
                        crmId = contactData.CrmId,
                    };
                    var request1 = new HttpRequestMessage(HttpMethod.Put, $"https://saleslife-neo4j-api-811039469089.europe-north1.run.app/api/v2/74B6A983")
                    {
                        // Optionally set content if the POST requires a body
                        Content = new StringContent(JsonConvert.SerializeObject(payload), System.Text.Encoding.UTF8, "application/json")
                    };
                    var TokenData = await _context.Tokens.FirstOrDefaultAsync(w => w.UserId == userId && w.IsActive);
                    request1.Headers.Add("authToken", TokenData?.Id.ToString());
                    // Fire-and-forget the Neo4j API call
                    Task.Run(async () =>
                    {
                        try
                        {
                            var response1 = await httpClient1.SendAsync(request1);
                            if (!response1.IsSuccessStatusCode)
                            {
                                // Optionally log the failure or handle it as needed
                                Console.WriteLine($"Neo4j API call failed with status code: {response1.StatusCode}");
                            }
                            else
                            {
                                // Optionally log the success
                                Console.WriteLine("Neo4j API call succeeded.");
                            }
                        }
                        catch (Exception ex)
                        {
                            // Optionally log the exception
                            Console.WriteLine($"Neo4j API call failed: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {

            }

            return Ok();
        }

        [HttpDelete("374EAE39/{id}")]
        public async Task<IActionResult> DeleteContact(Guid id)
        {
            var userId = await _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            var result = await _contactService.DeleteContactAsync(id);
            if (!result)
            {
                var userData = await _context.Users.FirstOrDefaultAsync(w => w.Id == userId && w.IsActive);
                var customerData = await _context.Customers.FirstOrDefaultAsync(w => w.OrgId == userData.OrgId && w.IsActive);
                using var dbContext = await _dbContextProvider.GetDbContextAsync(customerData.Id);
                var checkForCContact = await dbContext.ccontact.FirstOrDefaultAsync(w => w.Id == id);
                if (checkForCContact != null)
                {
                    checkForCContact.IsActive = false;
                    checkForCContact.ModifiedDate = DateTime.UtcNow;
                    await dbContext.SaveChangesAsync();

                    // update in redis cache
                    string cacheKey = $"contacts_{userId}";
                    var cached = await _redisService.GetStringAsync(cacheKey);
                    if (!string.IsNullOrEmpty(cached))
                    {
                        var cachedModel = System.Text.Json.JsonSerializer.Deserialize<List<Contact>>(cached);

                        var contactToUpdate = cachedModel.FirstOrDefault(c => c.Id == checkForCContact.Id);
                        contactToUpdate.IsActive = false;

                        var updatedSerialized = System.Text.Json.JsonSerializer.Serialize(cachedModel);
                        await _redisService.SetStringAsync(cacheKey, updatedSerialized, TimeSpan.FromHours(72));
                    }

                    try
                    {
                        // track contact deletion
                        await _trackService.CreateTrack(userId, new TrackPayload()
                        {
                            Page = "Contact",
                            Element = "ContactDeleted",
                            PageAction = "Delete"
                        });
                    }
                    catch (Exception ex)
                    {

                    }
                }

            }
            else
            {
                // update in redis cache
                string cacheKey = $"contacts_{userId}";
                var cached = await _redisService.GetStringAsync(cacheKey);
                if (!string.IsNullOrEmpty(cached))
                {
                    var cachedModel = System.Text.Json.JsonSerializer.Deserialize<List<Contact>>(cached);

                    var contactToUpdate = cachedModel.FirstOrDefault(c => c.Id == id);
                    contactToUpdate.IsActive = false;

                    var updatedSerialized = System.Text.Json.JsonSerializer.Serialize(cachedModel);
                    await _redisService.SetStringAsync(cacheKey, updatedSerialized, TimeSpan.FromHours(72));
                }
            }

            try
            {
                // create user in neo4j
                using (var httpClient1 = new HttpClient())
                {
                    var request1 = new HttpRequestMessage(HttpMethod.Delete, $"https://saleslife-neo4j-api-811039469089.europe-north1.run.app/api/v2/contacts/{id}/7468FC36")
                    {
                        // Optionally set content if the POST requires a body
                    };
                    var TokenData = await _context.Tokens.FirstOrDefaultAsync(w => w.UserId == userId && w.IsActive);
                    request1.Headers.Add("authToken", TokenData?.Id.ToString());
                    var response1 = httpClient1.SendAsync(request1);
                }
            }
            catch (Exception ex)
            {

            }

            return NoContent();
        }

        [HttpGet("contacts")]
        public async Task<IActionResult> GetContacts([FromHeader(Name = "X-Api-Key")] string apiKey)
        {
            var (userEmail, isAdmin) = AuthenticateUser(apiKey);
            if (string.IsNullOrEmpty(userEmail))
            {
                return Unauthorized("Invalid API key");
            }

            if (isAdmin)
            {
                var response1 = await _httpClient.GetAsync("https://api.hubapi.com/crm/v3/objects/contacts");
                var content1 = await response1.Content.ReadAsStringAsync();
                return Ok(content1);
            }

            var ownerId = await GetOwnerIdByEmailAsync(userEmail);
            if (string.IsNullOrEmpty(ownerId))
            {
                return BadRequest("User is not a HubSpot owner");
            }

            var requestBody = new
            {
                filterGroups = new[]
                {
                     new { filters = new[] { new { propertyName = "hubspot_owner_id", @operator = "EQ", value = ownerId } } }
                }
            };
            var response = await _httpClient.PostAsJsonAsync("https://api.hubapi.com/crm/v3/objects/contacts/search", requestBody);
            var content = await response.Content.ReadAsStringAsync();
            return Ok(content);
        }
        private (string userEmail, bool isAdmin) AuthenticateUser(string apiKey)
        {
            // Example: Replace with your actual authentication system
            var mockUsers = new Dictionary<string, (string email, bool isAdmin)>
            {
                { "admin-key-123", ("<EMAIL>", true) },
                { "user-key-456", ("<EMAIL>", false) }
            };

            return mockUsers.TryGetValue(apiKey, out var user) ? user : (null, false);
        }

        private async Task<string> GetOwnerIdByEmailAsync(string email)
        {
            var response = await _httpClient.GetAsync($"https://api.hubapi.com/crm/v3/owners?email={email}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var owner = System.Text.Json.JsonSerializer.Deserialize<OwnerResponse>(json);
                return owner?.Results?.FirstOrDefault()?.Id;
            }
            return null;
        }

        // Simple response model for owners API
        private class OwnerResponse
        {
            public List<Owner> Results { get; set; }
            public class Owner { public string Id { get; set; } public string Email { get; set; } }
        }

        [HttpGet("0DD9175F")]
        public async Task<ActionResult<IEnumerable<Contact>>> SearchContacts(
            [FromQuery] string query,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 30)
        {
            var userId = await _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            query = query.ToLower();
            var userData = await _context.Users.FirstOrDefaultAsync(w => w.Id == userId && w.IsActive);
            var userEmail = await _context.UserEmails.FirstOrDefaultAsync(w => w.UserId == userId && w.IsActive);

            List<Contact> allContacts = new List<Contact>();
            try
            {
                var customerData = await _context.Customers.FirstOrDefaultAsync(w => w.OrgId == userData.OrgId && w.IsActive);
                using var dbContext = await _dbContextProvider.GetDbContextAsync(customerData.Id);

                var ownerMappingData = await dbContext.userownermapping.FirstOrDefaultAsync(w => w.useremailid == userEmail.Email);
                if (ownerMappingData != null)
                {
                    var ownerId = ownerMappingData.ownerid;

                    var crmContacts = await dbContext.ccontact
                        .Where(w => (w.OwnerId == ownerId || w.UserId == userId) && w.IsActive == true &&
                                    (w.DisplayName.ToLower().Contains(query) || w.Email.ToLower().Contains(query) || w.Linkedin.ToLower().Contains(query)))
                        .ToListAsync();

                    foreach (var contactData in crmContacts)
                    {
                        Contact contact = new Contact()
                        {
                            Id = contactData.Id,
                            UserId = userId,
                            OrgId = userData.OrgId,
                            CrmId = contactData.CrmId,
                            FirstName = contactData.FirstName,
                            LastName = contactData.LastName,
                            DisplayName = contactData.DisplayName,
                            Email = contactData.Email,
                            Designation = contactData.Designation,
                            Linkedin = !string.IsNullOrEmpty(contactData.Linkedin) ? contactData.Linkedin : contactData.hs_linkedin_url,
                            IsActive = contactData.IsActive,
                            CreatedDate = contactData.CreatedDate,
                            ModifiedDate = contactData.ModifiedDate,
                            ProviderId = contactData.ProviderId,
                            IsEnriched = contactData.IsEnriched
                        };
                        allContacts.Add(contact);
                    }
                }
                else
                {
                    var crmContacts = await dbContext.ccontact
                        .Where(w => w.UserId == userId && w.IsActive == true &&
                                    (w.DisplayName.ToLower().Contains(query) || w.Email.ToLower().Contains(query)))
                        .ToListAsync();

                    foreach (var contactData in crmContacts)
                    {
                        Contact contact = new Contact()
                        {
                            Id = contactData.Id,
                            UserId = userId,
                            OrgId = userData.OrgId,
                            CrmId = contactData.CrmId,
                            FirstName = contactData.FirstName,
                            LastName = contactData.LastName,
                            DisplayName = contactData.DisplayName,
                            Email = contactData.Email,
                            Designation = contactData.Designation,
                            Linkedin = !string.IsNullOrEmpty(contactData.Linkedin) ? contactData.Linkedin : contactData.hs_linkedin_url,
                            IsActive = contactData.IsActive,
                            CreatedDate = contactData.CreatedDate,
                            ModifiedDate = contactData.ModifiedDate,
                            ProviderId = contactData.ProviderId,
                            IsEnriched = contactData.IsEnriched
                        };
                        allContacts.Add(contact);
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle exception (log it, etc.)
            }

            var contacts = await _contactService.SearchContactsAsync(userId, query);
            allContacts.AddRange(contacts);

            // Apply pagination to the combined list
            var skipCount = (page - 1) * pageSize;

            // Ensure skipCount does not exceed the total count of allContacts
            if (skipCount >= allContacts.Count)
            {
                skipCount = 0; // Reset skipCount to 0 to return all contacts
            }

            var paginatedContacts = allContacts
                .Skip(skipCount)
                .Take(pageSize)
                .ToList();

            return Ok(paginatedContacts);
        }

        [HttpGet("F19EDEBA")]
        public async Task<ActionResult<object>> GetContactsWithHasMore(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 30,
            [FromQuery] string filter = "alphabetical")
        {
            var userId = await _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            var userData = await _context.Users.FirstOrDefaultAsync(w => w.Id == userId && w.IsActive);
            var userEmail = await _context.UserEmails.FirstOrDefaultAsync(w => w.UserId == userId && w.IsActive);

            List<Contact> allContacts = new List<Contact>();
            try
            {
                var customerData = await _context.Customers.FirstOrDefaultAsync(w => w.OrgId == userData.OrgId && w.IsActive);
                using var dbContext = await _dbContextProvider.GetDbContextAsync(customerData.Id);

                // Step 1: Check with ownerMappingData first
                var ownerMappingData = await dbContext.userownermapping
                    .Where(w => w.useremailid == userEmail.Email)
                    .ToListAsync();

                if (ownerMappingData.Any())
                {
                    var ownerIds = ownerMappingData.Select(w => w.ownerid).ToList();

                    var crmContactsByOwner = await dbContext.ccontact
                        .Where(w => ownerIds.Contains(w.OwnerId) && w.IsActive == true)
                        .ToListAsync();

                    foreach (var contactData in crmContactsByOwner)
                    {
                        Contact contact = new Contact()
                        {
                            Id = contactData.Id,
                            UserId = userId,
                            OrgId = userData.OrgId,
                            CrmId = contactData.CrmId,
                            FirstName = contactData.FirstName,
                            LastName = contactData.LastName,
                            DisplayName = contactData.DisplayName,
                            Email = contactData.Email,
                            Designation = contactData.Designation,
                            Linkedin = !string.IsNullOrEmpty(contactData.Linkedin) ? contactData.Linkedin : contactData.hs_linkedin_url,
                            IsActive = contactData.IsActive,
                            CreatedDate = contactData.CreatedDate,
                            ModifiedDate = contactData.ModifiedDate,
                            ProviderId = contactData.ProviderId,
                            IsEnriched = contactData.IsEnriched
                        };
                        allContacts.Add(contact);
                    }
                }

                // Step 2: Query contacts by UserId and IsActive
                var crmContactsQuery = dbContext.ccontact
                    .Where(w => w.UserId == userId && w.IsActive == true);

                // Apply sorting based on the filter
                if (filter.ToLower() == "latest")
                {
                    crmContactsQuery = crmContactsQuery.OrderByDescending(w => w.ModifiedDate);
                }
                else
                {
                    crmContactsQuery = crmContactsQuery.OrderBy(w => w.DisplayName);
                }

                var crmContactsByUser = await crmContactsQuery
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize + 1) // Fetch one extra record to determine if there are more
                    .ToListAsync();

                foreach (var contactData in crmContactsByUser)
                {
                    Contact contact = new Contact()
                    {
                        Id = contactData.Id,
                        UserId = userId,
                        OrgId = userData.OrgId,
                        CrmId = contactData.CrmId,
                        FirstName = contactData.FirstName,
                        LastName = contactData.LastName,
                        DisplayName = contactData.DisplayName,
                        Email = contactData.Email,
                        Designation = contactData.Designation,
                        Linkedin = !string.IsNullOrEmpty(contactData.Linkedin) ? contactData.Linkedin : contactData.hs_linkedin_url,
                        IsActive = contactData.IsActive,
                        CreatedDate = contactData.CreatedDate,
                        ModifiedDate = contactData.ModifiedDate,
                        ProviderId = contactData.ProviderId,
                        IsEnriched = contactData.IsEnriched
                    };
                    allContacts.Add(contact);
                }
            }
            catch (Exception ex)
            {
                // Handle exception (log it, etc.)
            }

            var contacts = await _contactService.GetAllContactAsync(userId);
            allContacts.AddRange(contacts);

            // Apply sorting to the combined list
            if (filter.ToLower() == "latest")
            {
                allContacts = allContacts.OrderByDescending(c => c.ModifiedDate).ToList();
            }
            else
            {
                allContacts = allContacts.OrderBy(c => c.DisplayName).ToList();
            }

            // Apply pagination to the sorted list and determine if there are more records
            var paginatedContacts = allContacts
                .Skip((page - 1) * pageSize)
                .Take(pageSize + 1) // Fetch one extra record to determine if there are more
                .ToList();

            var hasMore = paginatedContacts.Count > pageSize;
            paginatedContacts = paginatedContacts.Take(pageSize).ToList(); // Take only the requested page size

            return Ok(new
            {
                Contacts = paginatedContacts,
                HasMore = hasMore
            });
        }

        [HttpPost("8D1FD405")]
        public async Task<ActionResult<Company>> CreateContactOrGetContact([FromBody] ContactPayload contactPayload)
        {
            var userId = await _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            var userData = await _context.Users.FirstOrDefaultAsync(w => w.Id == userId);
            var customerData = await _context.Customers.FirstOrDefaultAsync(w => w.OrgId == userData.OrgId && w.IsActive);
            using var dbContext = await _dbContextProvider.GetDbContextAsync(customerData.Id);
            Guid contactId = Guid.Empty;
            bool isContactExists = false;
            if (!string.IsNullOrEmpty(contactPayload.Email))
            {
                var checkForContact = await dbContext.ccontact.FirstOrDefaultAsync(w => w.Email == contactPayload.Email && w.UserId == userId && w.IsActive == true);
                if (checkForContact != null)
                {
                    isContactExists = true;
                    contactId = checkForContact.Id;
                }
                if (!isContactExists)
                {
                    var checkForContactInContacts = await _context.Contacts.FirstOrDefaultAsync(w => w.Email == contactPayload.Email && w.UserId == userId && w.IsActive == true);
                    if (checkForContactInContacts != null)
                    {
                        isContactExists = true;
                        contactId = checkForContactInContacts.Id;
                    }
                }
            }
            else if (!string.IsNullOrEmpty(contactPayload.Linkedin))
            {
                var checkForContact = await dbContext.ccontact.FirstOrDefaultAsync(w => w.Linkedin == contactPayload.Linkedin && w.UserId == userId && w.IsActive == true);
                if (checkForContact != null)
                {
                    isContactExists = true;
                    contactId = checkForContact.Id;
                }
                if (!isContactExists)
                {
                    var checkForContactInContacts = await _context.Contacts.FirstOrDefaultAsync(w => w.Linkedin == contactPayload.Linkedin && w.UserId == userId && w.IsActive == true);
                    if (checkForContactInContacts != null)
                    {
                        isContactExists = true;
                        contactId = checkForContactInContacts.Id;
                    }
                }
            }

            if (!isContactExists)
            {
                CContact contact = new CContact();
                contact.Id = Guid.NewGuid();
                contact.CreatedDate = DateTime.UtcNow;
                contact.Designation = contactPayload.Designation;
                contact.Email = contactPayload.Email;
                contact.FirstName = contactPayload.FirstName;
                contact.LastName = contactPayload.LastName;
                contact.DisplayName = contactPayload.FirstName + " " + contactPayload.LastName;
                contact.IsActive = true;
                contact.Linkedin = contactPayload.Linkedin;
                contact.UserId = userId;
                contact.ModifiedDate = DateTime.UtcNow;
                contact.OrgId = _context.Users.FirstOrDefault(w => w.Id == userId).OrgId;
                contact.CustomerId = customerData.Id;
                contact.ProviderId = Guid.Empty;
                contact.IsSynced = false;  // Changed from "false" to false
                contact.IsEnriched = false;

                dbContext.ccontact.Add(contact);
                await dbContext.SaveChangesAsync();

                // store in the redis cache
                string cacheKey = $"contacts_{userId}";
                var cached = await _redisService.GetStringAsync(cacheKey);
                if (!string.IsNullOrEmpty(cached))
                {
                    var cachedModel = System.Text.Json.JsonSerializer.Deserialize<List<Contact>>(cached);
                    Contact contactData = new Contact()
                    {
                        Id = contact.Id,
                        UserId = userId,
                        OrgId = userData.OrgId,
                        CrmId = contact.CrmId,
                        FirstName = contact.FirstName,
                        LastName = contact.LastName,
                        DisplayName = contact.DisplayName,
                        Email = contact.Email,
                        Designation = contact.Designation,
                        Linkedin = !string.IsNullOrEmpty(contact.Linkedin) ? contact.Linkedin : contact.hs_linkedin_url,
                        IsActive = contact.IsActive,
                        CreatedDate = contact.CreatedDate,
                        ModifiedDate = contact.ModifiedDate,
                        ProviderId = contact.ProviderId,
                        IsEnriched = contact.IsEnriched
                    };
                    cachedModel.AddRange(contactData);
                    var serialized = System.Text.Json.JsonSerializer.Serialize(cachedModel);
                    await _redisService.SetStringAsync(cacheKey, serialized, TimeSpan.FromHours(72));
                }
                contactId = contact.Id;

                try
                {
                    // track contact creation
                    await _trackService.CreateTrack(userId, new TrackPayload()
                    {
                        Page = "Contact",
                        Element = "ContactAdded",
                        PageAction = "Create"
                    });
                }
                catch (Exception ex)
                {

                }
            }
            return Ok(new { contactId = contactId, name = contactPayload.FirstName, email = contactPayload.Email, linkedin = contactPayload.Linkedin });
        }

    }
}
