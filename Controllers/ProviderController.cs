using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using v2.backend.Models;
using v2.backend.Services;
using static v2.backend.Models.PropertyModels;

namespace v2.backend.Controllers
{
    public class ProviderController : Controller
    {
        private readonly AppDbContext _context;
        private readonly AuthService _authService;
        public ProviderController(AppDbContext context, AuthService authService)
        {
            _context = context;
            _authService = authService;
        }
        [HttpGet("F47F7540")]
        public async Task<ActionResult> GetRedirectUrlForProvider(Guid providerId)
        {
            var userId = await _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            try
            {
                var adminOrgProvider = await _context.OrgProviders.FirstOrDefaultAsync(w => w.OrgId == CommonData.AdminOrgId && w.ProviderId == providerId && w.IsActive == true);
                string finalUrl = "";
                switch (providerId.ToString())
                {
                    case "fe85816e-f851-4d0a-962b-861695ff6d76": // hubspot
                        var clientId = "f2469cc0-e61b-4a78-8d27-e1fe77cff03a";
                        var redirectUrl = "https://connectsaleslife.azurewebsites.net/Authenticated/fe85816ef8514d0a962b861695ff6d76";
                        var scope = "crm.objects.contacts.read crm.objects.deals.read crm.objects.companies.read crm.objects.contacts.write crm.objects.leads.read sales-email-read crm.objects.users.read oauth crm.objects.owners.read";
                        // var clientId = await _context.OrgProviderIdentifiers.Where(w => w.OrgProviderId == adminOrgProvider.Id && w.IdentifierId == Guid.Parse("f9bd483a-6c5d-40de-883a-9158bd89a7aa")).FirstOrDefaultAsync();
                        // var redirectUrl = await _context.OrgProviderIdentifiers.Where(w => w.OrgProviderId == adminOrgProvider.Id && w.IdentifierId == Guid.Parse("48422156-28ee-4a3c-9296-25b68ce283ab")).FirstOrDefaultAsync();
                        // var scope = await _context.OrgProviderIdentifiers.Where(w => w.OrgProviderId == adminOrgProvider.Id && w.IdentifierId == Guid.Parse("0e44c39f-4e08-407d-b4a0-983a01e74f22")).FirstOrDefaultAsync();
                        finalUrl = $"https://app.hubspot.com/oauth/authorize?client_id={clientId}&redirect_uri={redirectUrl}&scope={scope}";
                        break;

                }

                return Ok(finalUrl);
            }
            catch (Exception ex)
            {
                return Ok();
            }
        }
        // GET: api/providers - Grouped by ProviderTypeId
        [HttpGet("1866654F")]
        public async Task<ActionResult<IEnumerable<GroupedProviderResponse>>> GetProviders()
        {
            var userId = await _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            var groupedProviders = await _context.Providers
                .Join(_context.ProviderTypes,
                    p => p.ProviderTypeId,
                    pt => pt.Id,
                    (p, pt) => new ProviderDto
                    {
                        Id = p.Id,
                        ProviderTypeId = p.ProviderTypeId,
                        Name = p.Name,
                        Description = p.Description,
                        IsActive = p.IsActive,
                        ShowSequence = p.ShowSequence,
                        AuthType = p.AuthType,
                        IsVisible = p.IsVisible,
                        Status = p.Status,
                        ProviderTypeName = pt.Name
                    })
                .GroupBy(p => new { p.ProviderTypeId, p.ProviderTypeName })
                .Select(g => new GroupedProviderResponse
                {
                    ProviderTypeId = g.Key.ProviderTypeId,
                    ProviderTypeName = g.Key.ProviderTypeName,
                    Providers = g.ToList()
                })
                .ToListAsync();

            return Ok(groupedProviders);
        }

        // GET: api/providers/{id}
        [HttpGet("C961D0B6/{id}")]
        public async Task<ActionResult<ProviderDto>> GetProvider(Guid id)
        {
            var provider = await _context.Providers
                .FirstOrDefaultAsync(p => p.Id == id);

            if (provider == null) return NotFound();

            var providerDto = new ProviderDto
            {
                Id = provider.Id,
                ProviderTypeId = provider.ProviderTypeId,
                Name = provider.Name,
                ProviderTypeName = _context.ProviderTypes.FirstOrDefault(w => w.Id == provider.ProviderTypeId).Name
            };

            return Ok(providerDto);
        }

        // POST: api/providers
        [HttpPost("5DF6750A")]
        public async Task<ActionResult<Provider>> CreateProvider(ProviderDto providerDto)
        {
            var provider = new Provider
            {
                Id = Guid.NewGuid(),
                ProviderTypeId = providerDto.ProviderTypeId,
                Name = providerDto.Name,
                Description = providerDto.Description,
                IsActive = providerDto.IsActive,
                ShowSequence = providerDto.ShowSequence,
                AuthType = providerDto.AuthType,
                IsVisible = providerDto.IsVisible,
                Status = providerDto.Status,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };

            _context.Providers.Add(provider);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetProvider), new { id = provider.Id }, provider);
        }

        // PUT: api/providers/{id}
        [HttpPut("244F0030/{id}")]
        public async Task<IActionResult> UpdateProvider(Guid id, ProviderDto providerDto)
        {
            var provider = await _context.Providers.FindAsync(id);
            if (provider == null) return NotFound();

            // Update properties
            provider.Name = providerDto.Name;
            provider.Description = providerDto.Description;
            provider.IsActive = providerDto.IsActive;
            provider.ShowSequence = providerDto.ShowSequence;
            provider.AuthType = providerDto.AuthType;
            provider.IsVisible = providerDto.IsVisible;
            provider.Status = providerDto.Status;
            provider.ProviderTypeId = providerDto.ProviderTypeId;
            provider.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return NoContent();
        }

        // DELETE: api/providers/{id}
        [HttpDelete("3468E1CF/{id}")]
        public async Task<IActionResult> DeleteProvider(Guid id)
        {
            var provider = await _context.Providers.FindAsync(id);
            if (provider == null) return NotFound();

            _context.Providers.Remove(provider);
            await _context.SaveChangesAsync();
            return NoContent();
        }
        // GET: api/providertypes
        [HttpGet("E6574773")]
        public async Task<ActionResult<IEnumerable<ProviderTypeDto>>> GetProviderTypes()
        {
            var providerTypes = await _context.ProviderTypes.Where(w=>w.IsActive == true)
                .Select(pt => new ProviderTypeDto
                {
                    Id = pt.Id,
                    Name = pt.Name,
                    Details = pt.Details,
                    IsActive = pt.IsActive,
                    ShowSequence = pt.ShowSequence
                })
                .ToListAsync();

            return Ok(providerTypes);
        }

        // GET: api/providertypes/{id}
        [HttpGet("BE9990CC/{id}")]
        public async Task<ActionResult<ProviderTypeDto>> GetProviderType(Guid id)
        {
            var providerType = await _context.ProviderTypes.FindAsync(id);
            if (providerType == null) return NotFound();

            var providerTypeDto = new ProviderTypeDto
            {
                Id = providerType.Id,
                Name = providerType.Name,
                Details = providerType.Details,
                IsActive = providerType.IsActive,
                ShowSequence = providerType.ShowSequence
            };

            return Ok(providerTypeDto);
        }

        // POST: api/providertypes
        [HttpPost("4165D40B")]
        public async Task<ActionResult<ProviderType>> CreateProviderType(ProviderTypeDto providerTypeDto)
        {
            var providerType = new ProviderType
            {
                Id = Guid.NewGuid(),
                Name = providerTypeDto.Name,
                Details = providerTypeDto.Details,
                IsActive = providerTypeDto.IsActive,
                ShowSequence = providerTypeDto.ShowSequence,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };

            _context.ProviderTypes.Add(providerType);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetProviderType), new { id = providerType.Id }, providerType);
        }

        // PUT: api/providertypes/{id}
        [HttpPut("AFA59C4A/{id}")]
        public async Task<IActionResult> UpdateProviderType(Guid id, ProviderTypeDto providerTypeDto)
        {
            var providerType = await _context.ProviderTypes.FindAsync(id);
            if (providerType == null) return NotFound();

            // Update properties
            providerType.Name = providerTypeDto.Name;
            providerType.Details = providerTypeDto.Details;
            providerType.IsActive = providerTypeDto.IsActive;
            providerType.ShowSequence = providerTypeDto.ShowSequence;
            providerType.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return NoContent();
        }

        // DELETE: api/providertypes/{id}
        [HttpDelete("9B993AA1/{id}")]
        public async Task<IActionResult> DeleteProviderType(Guid id)
        {
            var providerType = await _context.ProviderTypes.FindAsync(id);
            if (providerType == null) return NotFound();

            _context.ProviderTypes.Remove(providerType);
            await _context.SaveChangesAsync();
            return NoContent();
        }

        // POST: api/identifier
        [HttpPost("288131E0")]
        public async Task<ActionResult<Provider>> CreateIdentifier(IdentifierDto identifierDto)
        {
            var identifier = new Identifier
            {
                Id = Guid.NewGuid(),
                IsActive = true,
                Name = identifierDto.Name,
                ProviderId = identifierDto.ProviderId,
                Value = string.IsNullOrEmpty(identifierDto.Value) ? "" : identifierDto.Value,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };

            _context.Identifiers.Add(identifier);
            await _context.SaveChangesAsync();

            return Ok();
        }

        // POST: api/identifier
        [HttpPost("388131E1")]
        public async Task<ActionResult<Provider>> CreateOrg(OrgDto orgDto)
        {
            var org = new Org
            {
                Id = Guid.NewGuid(),
                IsActive = true,
                Name = orgDto.Name,
                Domain = orgDto.Domain,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };

            _context.Orgs.Add(org);
            await _context.SaveChangesAsync();

            return Ok();
        }
        // POST: api/identifier
        [HttpPost("488131E1")]
        public async Task<ActionResult<Provider>> CreateOrgProviderWithIdentifier([FromBody] OrgProviderDto orgDto)
        {
            var orgProvider = new OrgProvider
            {
                Id = Guid.NewGuid(),
                IsActive = true,
                OrgId = orgDto.OrgId,
                ProviderId = orgDto.ProviderId,
                ProviderTypeId = orgDto.ProviderTypeId,
                CreatedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };

            _context.OrgProviders.Add(orgProvider);

            foreach (var oi in orgDto.orgProviderIdentifierDtos)
            {
                var orgProviderIdentifier = new OrgProviderIdentifier
                {
                    Id = Guid.NewGuid(),
                    CreatedDate = DateTime.UtcNow,
                    IdentifierId = oi.IdentifierId,
                    ModifiedDate = DateTime.UtcNow,
                    OrgProviderId = orgProvider.Id,
                    Value = oi.Value
                };
                _context.OrgProviderIdentifiers.Add(orgProviderIdentifier);
            }
            await _context.SaveChangesAsync();

            return Ok();
        }

        // POST: api/identifier
        [HttpPost("588131E1")]
        public async Task<ActionResult<Provider>> CreateFreeDomain([FromBody] FreDomainDto freeDomainDto)
        {
            foreach (var freedomain in freeDomainDto.Name)
            {
                var freeDomain = new FreeDomain
                {
                    DomainName = freedomain,
                    Id = Guid.NewGuid()
                };
                _context.FreeDomains.Add(freeDomain);
            }
            await _context.SaveChangesAsync();

            return Ok();
        }


    }
}
